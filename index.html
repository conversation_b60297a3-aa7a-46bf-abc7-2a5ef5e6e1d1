<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的世界 - 网页版</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #87CEEB;
            font-family: 'Courier New', monospace;
            overflow: hidden;
        }
        
        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #gameCanvas {
            display: block;
            cursor: none;
        }
        
        #ui {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            z-index: 100;
            background: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 5px;
        }
        
        #crosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            z-index: 100;
            pointer-events: none;
        }
        
        #crosshair::before,
        #crosshair::after {
            content: '';
            position: absolute;
            background: white;
        }
        
        #crosshair::before {
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            transform: translateY(-50%);
        }
        
        #crosshair::after {
            left: 50%;
            top: 0;
            bottom: 0;
            width: 2px;
            transform: translateX(-50%);
        }
        
        #inventory {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 5px;
            z-index: 100;
        }
        
        .inventory-slot {
            width: 50px;
            height: 50px;
            background: rgba(0, 0, 0, 0.7);
            border: 2px solid #666;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            cursor: pointer;
        }
        
        .inventory-slot.active {
            border-color: white;
            background: rgba(255, 255, 255, 0.2);
        }
        
        #instructions {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            z-index: 200;
        }
        
        #instructions.hidden {
            display: none;
        }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        button:hover {
            background: #45a049;
        }

        #pointerLockHint {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            z-index: 150;
            border: 2px solid #4CAF50;
        }

        #pointerLockHint div {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas"></canvas>
        
        <div id="crosshair"></div>
        
        <div id="ui">
            <div>我的世界 - 网页版</div>
            <div>位置: <span id="position">0, 0, 0</span></div>
            <div>方块: <span id="blockCount">0</span></div>
            <div>视角: <span id="cameraMode">第一人称</span></div>
        </div>

        <div id="pointerLockHint" style="display: none;">
            <div>点击屏幕重新进入游戏</div>
            <div>按ESC键可以退出游戏模式</div>
        </div>
        
        <div id="inventory">
            <div class="inventory-slot active" data-block="grass">草地</div>
            <div class="inventory-slot" data-block="stone">石头</div>
            <div class="inventory-slot" data-block="wood">木头</div>
            <div class="inventory-slot" data-block="sand">沙子</div>
            <div class="inventory-slot" data-block="water">水</div>
        </div>
        
        <div id="instructions">
            <h2>欢迎来到我的世界！</h2>
            <p><strong>控制说明：</strong></p>
            <p>WASD - 移动</p>
            <p>鼠标 - 转动视角</p>
            <p>左键 - 破坏方块</p>
            <p>右键 - 放置方块</p>
            <p>空格 - 跳跃</p>
            <p>F键 - 切换第一/第三人称视角</p>
            <p>T键 - 测试方块放置</p>
            <p>ESC键 - 退出游戏模式</p>
            <p>数字键1-5 - 选择方块类型</p>
            <button onclick="startGame()">开始游戏</button>
        </div>
    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="src/main.js"></script>
</body>
</html>
