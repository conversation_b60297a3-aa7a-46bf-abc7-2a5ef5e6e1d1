# 🎮 我的世界 - 网页版

一个基于Web技术的3D方块世界游戏，类似于Minecraft的简化版本。

## 🌟 游戏特性

- **3D方块世界** - 使用Three.js渲染的立体世界
- **双视角模式** - 第一人称和第三人称视角自由切换
- **玩家模型** - 第三人称时显示类似史蒂夫的角色模型
- **方块建造系统** - 放置和破坏不同类型的方块
- **随机世界生成** - 自动生成地形和树木
- **物理系统** - 重力和碰撞检测
- **多种方块类型** - 草地、石头、木头、沙子、水

## 🎯 游戏控制

| 操作 | 按键 |
|------|------|
| 移动 | W, A, S, D |
| 转动视角 | 鼠标移动 |
| 跳跃 | 空格键 |
| 切换视角 | F键 |
| 破坏方块 | 鼠标左键 |
| 放置方块 | 鼠标右键 |
| 选择方块 | 数字键 1-5 或点击物品栏 |

## 🚀 如何运行

### 方法1: 使用Python服务器（推荐）

1. 确保你的电脑安装了Python 3
2. 在项目目录中运行：
   ```bash
   python server.py
   ```
3. 浏览器会自动打开游戏页面
4. 点击"开始游戏"按钮开始玩耍！

### 方法2: 使用其他本地服务器

如果你有其他HTTP服务器工具，也可以使用：

- **Node.js**: `npx serve .`
- **PHP**: `php -S localhost:8000`
- **Live Server** (VS Code扩展)

## 🎨 方块类型

1. **草地** (绿色) - 基础地面方块
2. **石头** (灰色) - 坚硬的建筑材料
3. **木头** (棕色) - 来自树木的材料
4. **沙子** (黄色) - 沙漠风格方块
5. **水** (蓝色) - 装饰性液体方块

## 🛠️ 技术栈

- **HTML5** - 游戏界面结构
- **CSS3** - 样式和UI设计
- **JavaScript (ES6+)** - 游戏逻辑
- **Three.js** - 3D图形渲染
- **WebGL** - 硬件加速渲染

## 📁 项目结构

```
myWorld/
├── index.html          # 主页面
├── src/
│   └── main.js         # 游戏主逻辑
├── server.py           # Python HTTP服务器
└── README.md           # 项目说明
```

## 🎮 游戏玩法建议

1. **探索世界** - 四处走动，熟悉环境
2. **建造房屋** - 使用不同方块建造你的家
3. **创造艺术** - 发挥创意，建造雕塑或建筑
4. **地形改造** - 挖掘山洞或建造山峰

## 🔧 自定义和扩展

游戏代码结构清晰，易于扩展：

- 在 `blockTypes` 对象中添加新的方块类型
- 修改 `generateWorld()` 函数来创建不同的地形
- 调整物理参数来改变游戏手感
- 添加新的UI元素和功能

## 🐛 已知问题

- 世界大小有限（41x41方块）
- 没有保存/加载功能
- 简化的物理系统
- 基础的光照效果

## 🚧 未来计划

- [ ] 无限世界生成
- [ ] 更多方块类型和材质
- [ ] 游戏存档功能
- [ ] 多人游戏支持
- [ ] 更复杂的地形生成
- [ ] 音效和背景音乐

## 📄 许可证

本项目仅供学习和娱乐使用。

---

**享受建造的乐趣！** 🏗️✨
