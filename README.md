# 🎮 我的世界 - 网页版

一个基于Web技术的3D方块世界游戏，类似于Minecraft的简化版本。

## 🌟 游戏特性

- **3D方块世界** - 使用Three.js渲染的立体世界
- **双视角模式** - 第一人称和第三人称视角自由切换
- **生动的角色动画** - 走路时腿部摆动、手臂摆动、身体晃动
- **沉浸式视角晃动** - 走路时相机轻微晃动，增强真实感
- **工具动画** - 破坏方块时显示镐子并有挥动动作
- **方块建造系统** - 放置和破坏不同类型的方块
- **随机世界生成** - 自动生成地形和树木
- **物理系统** - 重力和碰撞检测
- **多种方块类型** - 草地、石头、木头、沙子、水

## 🎯 游戏控制

| 操作 | 按键 |
|------|------|
| 移动 | W, A, S, D |
| 转动视角 | 鼠标移动 |
| 跳跃 | 空格键 |
| 切换视角 | F键 |
| 退出游戏模式 | ESC键 |
| 重新进入游戏 | 点击屏幕 |
| 破坏方块 | 鼠标左键 |
| 放置方块 | 鼠标右键 |
| 选择方块 | 数字键 1-5 或点击物品栏 |

## 🎮 游戏模式切换

### 进入游戏模式
- 点击"开始游戏"按钮或点击游戏画面
- 鼠标指针会被锁定，十字准星出现
- 可以使用鼠标控制视角

### 退出游戏模式
- 按**ESC键**主动退出
- 鼠标指针解锁，可以自由移动
- 会显示"点击屏幕重新进入游戏"提示

### 重新进入游戏
- 在退出游戏模式后，点击游戏画面任意位置
- 自动重新锁定鼠标指针
- 恢复游戏控制

## 🚀 如何运行

### 方法1: 使用Python服务器（推荐）

1. 确保你的电脑安装了Python 3
2. 在项目目录中运行：
   ```bash
   python server.py
   ```
3. 浏览器会自动打开游戏页面
4. 点击"开始游戏"按钮开始玩耍！

### 方法2: 使用其他本地服务器

如果你有其他HTTP服务器工具，也可以使用：

- **Node.js**: `npx serve .`
- **PHP**: `php -S localhost:8000`
- **Live Server** (VS Code扩展)

## 🎨 方块类型

1. **草地** (绿色) - 基础地面方块
2. **石头** (灰色) - 坚硬的建筑材料
3. **木头** (棕色) - 来自树木的材料
4. **沙子** (黄色) - 沙漠风格方块
5. **水** (蓝色) - 装饰性液体方块

## 🎭 动画特性

### 🚶‍♂️ 走路动画
- **腿部摆动** - 左右腿交替前后摆动
- **手臂摆动** - 与腿部相反方向的自然摆动
- **身体晃动** - 走路时身体和头部的轻微晃动
- **视角晃动** - 第一人称和第三人称都有走路时的视角晃动

### 🔨 破坏动画
- **工具显示** - 破坏方块时自动显示镐子
- **手臂挥动** - 右臂做出挥动镐子的动作
- **身体转动** - 破坏时身体轻微转动增加真实感
- **工具跟随** - 镐子跟随手臂旋转

### 🎮 视角效果
- **第一人称晃动** - 走路时相机上下轻微晃动
- **第三人称跟随** - 相机平滑跟随玩家并保持合适距离
- **动态调整** - 根据玩家动作调整相机位置

## 🛠️ 技术栈

- **HTML5** - 游戏界面结构
- **CSS3** - 样式和UI设计
- **JavaScript (ES6+)** - 游戏逻辑
- **Three.js** - 3D图形渲染
- **WebGL** - 硬件加速渲染

## 📁 项目结构

```
myWorld/
├── index.html          # 主页面
├── src/
│   └── main.js         # 游戏主逻辑
├── server.py           # Python HTTP服务器
└── README.md           # 项目说明
```

## 🎮 游戏玩法建议

1. **探索世界** - 四处走动，熟悉环境
2. **建造房屋** - 使用不同方块建造你的家
3. **创造艺术** - 发挥创意，建造雕塑或建筑
4. **地形改造** - 挖掘山洞或建造山峰

## 🔧 自定义和扩展

游戏代码结构清晰，易于扩展：

- 在 `blockTypes` 对象中添加新的方块类型
- 修改 `generateWorld()` 函数来创建不同的地形
- 调整物理参数来改变游戏手感
- 添加新的UI元素和功能

## 🐛 已知问题

- 世界大小有限（41x41方块）
- 没有保存/加载功能
- 简化的物理系统
- 基础的光照效果

## 🚧 未来计划

- [ ] 无限世界生成
- [ ] 更多方块类型和材质
- [ ] 游戏存档功能
- [ ] 多人游戏支持
- [ ] 更复杂的地形生成
- [ ] 音效和背景音乐

## 📄 许可证

本项目仅供学习和娱乐使用。

---

**享受建造的乐趣！** 🏗️✨
