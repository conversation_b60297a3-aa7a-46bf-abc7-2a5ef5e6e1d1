// 我的世界 - 网页版游戏主文件
class MinecraftGame {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.world = {};
        this.player = {
            position: new THREE.Vector3(0, 10, 0),
            velocity: new THREE.Vector3(0, 0, 0),
            onGround: false,
            model: null, // 玩家模型（第三人称时显示）
            parts: {}, // 存储身体各部分的引用
            animations: {
                walkCycle: 0, // 走路动画周期
                armSwing: 0, // 手臂挥动动画
                isWalking: false, // 是否在走路
                isBreaking: false, // 是否在破坏方块
                bobOffset: 0 // 视角晃动偏移
            }
        };
        this.controls = {
            forward: false,
            backward: false,
            left: false,
            right: false,
            jump: false
        };
        this.mouse = {
            x: 0,
            y: 0,
            sensitivity: 0.002
        };
        this.cameraSettings = {
            mode: 'first', // 'first' 或 'third'
            distance: 5, // 第三人称距离
            height: 2 // 第三人称高度偏移
        };
        this.selectedBlock = 'grass';
        this.blockTypes = {
            grass: 0x7CFC00,
            stone: 0x808080,
            wood: 0x8B4513,
            sand: 0xF4A460,
            water: 0x0077BE
        };
        this.raycaster = new THREE.Raycaster();
        this.blockCount = 0;

        this.init();
    }
    
    init() {
        // 创建场景
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x87CEEB);
        
        // 创建相机
        this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        this.camera.position.copy(this.player.position);
        
        // 创建渲染器
        const canvas = document.getElementById('gameCanvas');
        this.renderer = new THREE.WebGLRenderer({ canvas: canvas, antialias: true });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        // 添加光照
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(50, 50, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight);
        
        // 生成初始世界
        this.generateWorld();

        // 创建玩家模型
        this.createPlayerModel();

        // 设置事件监听
        this.setupEventListeners();

        // 开始游戏循环
        this.gameLoop();
    }
    
    generateWorld() {
        const size = 20;
        const height = 5;
        
        for (let x = -size; x <= size; x++) {
            for (let z = -size; z <= size; z++) {
                for (let y = 0; y < height; y++) {
                    let blockType = 'stone';
                    if (y === height - 1) {
                        blockType = 'grass';
                    } else if (y === height - 2) {
                        blockType = Math.random() > 0.7 ? 'stone' : 'grass';
                    }
                    
                    this.addBlock(x, y, z, blockType);
                }
            }
        }
        
        // 添加一些随机的树
        for (let i = 0; i < 10; i++) {
            const x = Math.floor(Math.random() * 40) - 20;
            const z = Math.floor(Math.random() * 40) - 20;
            this.generateTree(x, height, z);
        }
    }

    createPlayerModel() {
        // 创建简单的玩家模型（类似史蒂夫）
        const playerGroup = new THREE.Group();

        // 头部
        const headGeometry = new THREE.BoxGeometry(0.8, 0.8, 0.8);
        const headMaterial = new THREE.MeshLambertMaterial({ color: 0xFFDBB3 }); // 肤色
        const head = new THREE.Mesh(headGeometry, headMaterial);
        head.position.set(0, 1.4, 0);
        head.castShadow = true;
        playerGroup.add(head);

        // 身体
        const bodyGeometry = new THREE.BoxGeometry(0.8, 1.2, 0.4);
        const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x0066CC }); // 蓝色衣服
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.set(0, 0.6, 0);
        body.castShadow = true;
        playerGroup.add(body);

        // 左臂 - 设置旋转轴点
        const armGeometry = new THREE.BoxGeometry(0.4, 1.2, 0.4);
        const armMaterial = new THREE.MeshLambertMaterial({ color: 0xFFDBB3 });
        const leftArm = new THREE.Mesh(armGeometry, armMaterial);
        leftArm.position.set(-0.6, 0.6, 0);
        leftArm.geometry.translate(0, -0.6, 0); // 设置旋转轴点在肩膀
        leftArm.castShadow = true;
        playerGroup.add(leftArm);

        // 右臂 - 设置旋转轴点
        const rightArm = new THREE.Mesh(armGeometry.clone(), armMaterial);
        rightArm.position.set(0.6, 0.6, 0);
        rightArm.geometry.translate(0, -0.6, 0); // 设置旋转轴点在肩膀
        rightArm.castShadow = true;
        playerGroup.add(rightArm);

        // 左腿 - 设置旋转轴点
        const legGeometry = new THREE.BoxGeometry(0.4, 1.2, 0.4);
        const legMaterial = new THREE.MeshLambertMaterial({ color: 0x000080 }); // 深蓝色裤子
        const leftLeg = new THREE.Mesh(legGeometry, legMaterial);
        leftLeg.position.set(-0.2, -0.6, 0);
        leftLeg.geometry.translate(0, -0.6, 0); // 设置旋转轴点在臀部
        leftLeg.castShadow = true;
        playerGroup.add(leftLeg);

        // 右腿 - 设置旋转轴点
        const rightLeg = new THREE.Mesh(legGeometry.clone(), legMaterial);
        rightLeg.position.set(0.2, -0.6, 0);
        rightLeg.geometry.translate(0, -0.6, 0); // 设置旋转轴点在臀部
        rightLeg.castShadow = true;
        playerGroup.add(rightLeg);

        // 创建工具（锤子/镐子）
        const toolGroup = new THREE.Group();

        // 工具柄
        const handleGeometry = new THREE.BoxGeometry(0.1, 1.0, 0.1);
        const handleMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 }); // 棕色木柄
        const handle = new THREE.Mesh(handleGeometry, handleMaterial);
        handle.position.set(0, -0.3, 0);
        toolGroup.add(handle);

        // 工具头
        const toolHeadGeometry = new THREE.BoxGeometry(0.3, 0.3, 0.3);
        const toolHeadMaterial = new THREE.MeshLambertMaterial({ color: 0x808080 }); // 灰色金属
        const toolHead = new THREE.Mesh(toolHeadGeometry, toolHeadMaterial);
        toolHead.position.set(0, 0.2, 0);
        toolGroup.add(toolHead);

        // 将工具附加到右手
        toolGroup.position.set(0.3, -0.3, 0);
        toolGroup.visible = false; // 默认隐藏
        rightArm.add(toolGroup);

        // 存储身体各部分的引用
        this.player.parts = {
            head: head,
            body: body,
            leftArm: leftArm,
            rightArm: rightArm,
            leftLeg: leftLeg,
            rightLeg: rightLeg,
            tool: toolGroup
        };

        // 设置玩家模型位置
        playerGroup.position.copy(this.player.position);
        playerGroup.visible = false; // 默认第一人称，不显示模型

        this.player.model = playerGroup;
        this.scene.add(playerGroup);
    }

    generateTree(x, y, z) {
        // 树干
        for (let i = 0; i < 4; i++) {
            this.addBlock(x, y + i, z, 'wood');
        }
        
        // 树叶
        for (let dx = -1; dx <= 1; dx++) {
            for (let dz = -1; dz <= 1; dz++) {
                for (let dy = 0; dy < 2; dy++) {
                    if (Math.random() > 0.3) {
                        this.addBlock(x + dx, y + 3 + dy, z + dz, 'grass');
                    }
                }
            }
        }
    }
    
    addBlock(x, y, z, type) {
        const geometry = new THREE.BoxGeometry(1, 1, 1);
        const material = new THREE.MeshLambertMaterial({ color: this.blockTypes[type] });
        const block = new THREE.Mesh(geometry, material);
        
        block.position.set(x, y, z);
        block.castShadow = true;
        block.receiveShadow = true;
        block.userData = { type: type, x: x, y: y, z: z };
        
        this.scene.add(block);
        this.world[`${x},${y},${z}`] = block;
        this.blockCount++;
        this.updateUI();
    }
    
    removeBlock(x, y, z) {
        const key = `${x},${y},${z}`;
        if (this.world[key]) {
            this.scene.remove(this.world[key]);
            delete this.world[key];
            this.blockCount--;
            this.updateUI();
        }
    }
    
    getBlock(x, y, z) {
        return this.world[`${x},${y},${z}`];
    }
    
    setupEventListeners() {
        // 键盘事件
        document.addEventListener('keydown', (event) => {
            switch(event.code) {
                case 'KeyW': this.controls.forward = true; break;
                case 'KeyS': this.controls.backward = true; break;
                case 'KeyA': this.controls.left = true; break;
                case 'KeyD': this.controls.right = true; break;
                case 'Space': 
                    event.preventDefault();
                    this.controls.jump = true; 
                    break;
                case 'Digit1': this.selectBlock('grass'); break;
                case 'Digit2': this.selectBlock('stone'); break;
                case 'Digit3': this.selectBlock('wood'); break;
                case 'Digit4': this.selectBlock('sand'); break;
                case 'Digit5': this.selectBlock('water'); break;
                case 'KeyF': this.toggleCameraMode(); break; // F键切换视角
            }
        });
        
        document.addEventListener('keyup', (event) => {
            switch(event.code) {
                case 'KeyW': this.controls.forward = false; break;
                case 'KeyS': this.controls.backward = false; break;
                case 'KeyA': this.controls.left = false; break;
                case 'KeyD': this.controls.right = false; break;
                case 'Space': this.controls.jump = false; break;
            }
        });
        
        // 鼠标事件
        document.addEventListener('mousemove', (event) => {
            if (document.pointerLockElement === document.getElementById('gameCanvas')) {
                this.mouse.x -= event.movementX * this.mouse.sensitivity; // 修复左右方向
                this.mouse.y += event.movementY * this.mouse.sensitivity;
                this.mouse.y = Math.max(-Math.PI/2, Math.min(Math.PI/2, this.mouse.y));
            }
        });
        
        document.addEventListener('click', (event) => {
            if (event.button === 0) { // 左键
                this.breakBlock();
            }
        });
        
        document.addEventListener('contextmenu', (event) => {
            event.preventDefault();
            this.placeBlock();
        });
        
        // 窗口大小调整
        window.addEventListener('resize', () => {
            this.camera.aspect = window.innerWidth / window.innerHeight;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(window.innerWidth, window.innerHeight);
        });
        
        // 物品栏点击
        document.querySelectorAll('.inventory-slot').forEach(slot => {
            slot.addEventListener('click', () => {
                this.selectBlock(slot.dataset.block);
            });
        });
    }
    
    selectBlock(type) {
        this.selectedBlock = type;
        document.querySelectorAll('.inventory-slot').forEach(slot => {
            slot.classList.remove('active');
        });
        document.querySelector(`[data-block="${type}"]`).classList.add('active');
    }

    toggleCameraMode() {
        if (this.cameraSettings.mode === 'first') {
            this.cameraSettings.mode = 'third';
            this.player.model.visible = true; // 显示玩家模型
        } else {
            this.cameraSettings.mode = 'first';
            this.player.model.visible = false; // 隐藏玩家模型
        }
        this.updateUI();
    }
    
    breakBlock() {
        const intersect = this.getTargetBlock();
        if (intersect) {
            const block = intersect.object;
            this.removeBlock(block.userData.x, block.userData.y, block.userData.z);

            // 触发破坏动画
            this.startBreakingAnimation();
        }
    }

    startBreakingAnimation() {
        this.player.animations.isBreaking = true;
        this.player.animations.armSwing = 0;

        // 显示工具
        if (this.player.parts.tool) {
            this.player.parts.tool.visible = true;
        }

        // 1秒后结束动画
        setTimeout(() => {
            this.player.animations.isBreaking = false;
            if (this.player.parts.tool) {
                this.player.parts.tool.visible = false;
            }
        }, 1000);
    }
    
    placeBlock() {
        const intersect = this.getTargetBlock();
        if (intersect) {
            const face = intersect.face;
            const block = intersect.object;
            const normal = face.normal.clone();
            
            const newX = block.userData.x + normal.x;
            const newY = block.userData.y + normal.y;
            const newZ = block.userData.z + normal.z;
            
            // 检查是否与玩家位置冲突
            const playerPos = this.player.position.clone().round();
            if (!(newX === playerPos.x && (newY === playerPos.y || newY === playerPos.y - 1) && newZ === playerPos.z)) {
                this.addBlock(newX, newY, newZ, this.selectedBlock);
            }
        }
    }
    
    getTargetBlock() {
        this.raycaster.setFromCamera(new THREE.Vector2(0, 0), this.camera);
        const intersects = this.raycaster.intersectObjects(Object.values(this.world));
        return intersects.length > 0 ? intersects[0] : null;
    }
    
    updatePlayer() {
        const speed = 0.1;
        const jumpPower = 0.15;
        
        // 应用重力
        this.player.velocity.y -= 0.01;
        
        // 移动控制
        const direction = new THREE.Vector3();
        if (this.controls.forward) direction.z -= 1;
        if (this.controls.backward) direction.z += 1;
        if (this.controls.left) direction.x -= 1;
        if (this.controls.right) direction.x += 1;

        // 检测是否在移动
        const isMoving = direction.length() > 0;
        this.player.animations.isWalking = isMoving && this.player.onGround;

        direction.normalize();
        direction.multiplyScalar(speed);

        // 应用相机旋转到移动方向
        direction.applyAxisAngle(new THREE.Vector3(0, 1, 0), this.mouse.x);

        this.player.velocity.x = direction.x;
        this.player.velocity.z = direction.z;
        
        // 跳跃
        if (this.controls.jump && this.player.onGround) {
            this.player.velocity.y = jumpPower;
            this.player.onGround = false;
        }
        
        // 更新位置
        this.player.position.add(this.player.velocity);
        
        // 碰撞检测
        this.checkCollisions();

        // 更新玩家模型位置和动画
        if (this.player.model) {
            this.player.model.position.copy(this.player.position);
            this.player.model.rotation.y = this.mouse.x; // 玩家模型跟随视角旋转
            this.updatePlayerAnimations();
        }

        // 更新相机
        this.updateCamera();

        this.updateUI();
    }
    
    checkCollisions() {
        const pos = this.player.position;
        const blockBelow = this.getBlock(Math.floor(pos.x), Math.floor(pos.y - 1), Math.floor(pos.z));
        
        if (blockBelow) {
            if (pos.y <= Math.floor(pos.y) + 1) {
                pos.y = Math.floor(pos.y) + 1;
                this.player.velocity.y = 0;
                this.player.onGround = true;
            }
        } else {
            this.player.onGround = false;
        }
        
        // 防止掉出世界
        if (pos.y < -10) {
            pos.set(0, 10, 0);
            this.player.velocity.set(0, 0, 0);
        }
    }

    updatePlayerAnimations() {
        const parts = this.player.parts;
        const animations = this.player.animations;

        // 走路动画
        if (animations.isWalking) {
            animations.walkCycle += 0.2;

            // 腿部摆动
            const legSwing = Math.sin(animations.walkCycle) * 0.5;
            parts.leftLeg.rotation.x = legSwing;
            parts.rightLeg.rotation.x = -legSwing;

            // 手臂摆动（与腿部相反）
            if (!animations.isBreaking) {
                parts.leftArm.rotation.x = -legSwing * 0.5;
                parts.rightArm.rotation.x = legSwing * 0.5;
            }

            // 身体轻微晃动
            parts.body.rotation.z = Math.sin(animations.walkCycle * 2) * 0.05;

            // 头部轻微晃动
            parts.head.rotation.z = Math.sin(animations.walkCycle * 2) * 0.03;

            // 视角晃动
            animations.bobOffset = Math.sin(animations.walkCycle * 2) * 0.05;
        } else {
            // 停止走路时重置姿势
            animations.walkCycle = 0;
            animations.bobOffset = 0;

            if (!animations.isBreaking) {
                parts.leftLeg.rotation.x = 0;
                parts.rightLeg.rotation.x = 0;
                parts.leftArm.rotation.x = 0;
                parts.rightArm.rotation.x = 0;
            }

            parts.body.rotation.z = 0;
            parts.head.rotation.z = 0;
        }

        // 破坏方块动画
        if (animations.isBreaking) {
            animations.armSwing += 0.3;

            // 右臂挥动
            const swingAngle = Math.sin(animations.armSwing) * 1.2 - 0.5;
            parts.rightArm.rotation.x = swingAngle;

            // 身体轻微转动
            parts.body.rotation.y = Math.sin(animations.armSwing) * 0.1;

            // 工具跟随手臂旋转
            if (parts.tool.visible) {
                parts.tool.rotation.x = swingAngle * 0.5;
            }
        } else {
            // 重置破坏动画
            if (parts.rightArm.rotation.x !== 0 && !animations.isWalking) {
                parts.rightArm.rotation.x = 0;
                parts.body.rotation.y = 0;
                if (parts.tool) {
                    parts.tool.rotation.x = 0;
                }
            }
        }
    }

    updateCamera() {
        const bobOffset = this.player.animations.bobOffset;

        if (this.cameraSettings.mode === 'first') {
            // 第一人称视角
            const cameraPos = this.player.position.clone();
            cameraPos.y += bobOffset; // 添加走路晃动效果

            this.camera.position.copy(cameraPos);
            this.camera.rotation.set(this.mouse.y + bobOffset * 0.5, this.mouse.x, bobOffset * 0.2);
        } else {
            // 第三人称视角
            const distance = this.cameraSettings.distance;
            const height = this.cameraSettings.height;

            // 计算相机位置（在玩家后方）
            const cameraX = this.player.position.x + Math.sin(this.mouse.x) * distance;
            const cameraZ = this.player.position.z + Math.cos(this.mouse.x) * distance;
            const cameraY = this.player.position.y + height + bobOffset; // 添加晃动效果

            this.camera.position.set(cameraX, cameraY, cameraZ);

            // 让相机看向玩家（添加轻微晃动）
            const lookAtY = this.player.position.y + 0.5 + bobOffset * 0.3;
            this.camera.lookAt(this.player.position.x, lookAtY, this.player.position.z);
        }
    }

    updateUI() {
        const pos = this.player.position;
        document.getElementById('position').textContent =
            `${Math.floor(pos.x)}, ${Math.floor(pos.y)}, ${Math.floor(pos.z)}`;
        document.getElementById('blockCount').textContent = this.blockCount;

        // 更新视角模式显示
        const cameraMode = this.cameraSettings.mode === 'first' ? '第一人称' : '第三人称';
        document.getElementById('cameraMode').textContent = cameraMode;
    }
    
    gameLoop() {
        requestAnimationFrame(() => this.gameLoop());
        
        this.updatePlayer();
        this.renderer.render(this.scene, this.camera);
    }
}

// 全局变量
let game;

function startGame() {
    document.getElementById('instructions').classList.add('hidden');
    
    // 请求指针锁定
    const canvas = document.getElementById('gameCanvas');
    canvas.requestPointerLock();
    
    // 创建游戏实例
    game = new MinecraftGame();
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('我的世界游戏已准备就绪！');
});
