// 我的世界 - 网页版游戏主文件
class MinecraftGame {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.world = {};
        this.player = {
            position: new THREE.Vector3(0, 10, 0),
            velocity: new THREE.Vector3(0, 0, 0),
            onGround: false
        };
        this.controls = {
            forward: false,
            backward: false,
            left: false,
            right: false,
            jump: false
        };
        this.mouse = {
            x: 0,
            y: 0,
            sensitivity: 0.002
        };
        this.selectedBlock = 'grass';
        this.blockTypes = {
            grass: 0x7CFC00,
            stone: 0x808080,
            wood: 0x8B4513,
            sand: 0xF4A460,
            water: 0x0077BE
        };
        this.raycaster = new THREE.Raycaster();
        this.blockCount = 0;
        
        this.init();
    }
    
    init() {
        // 创建场景
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x87CEEB);
        
        // 创建相机
        this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        this.camera.position.copy(this.player.position);
        
        // 创建渲染器
        const canvas = document.getElementById('gameCanvas');
        this.renderer = new THREE.WebGLRenderer({ canvas: canvas, antialias: true });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        // 添加光照
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(50, 50, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight);
        
        // 生成初始世界
        this.generateWorld();
        
        // 设置事件监听
        this.setupEventListeners();
        
        // 开始游戏循环
        this.gameLoop();
    }
    
    generateWorld() {
        const size = 20;
        const height = 5;
        
        for (let x = -size; x <= size; x++) {
            for (let z = -size; z <= size; z++) {
                for (let y = 0; y < height; y++) {
                    let blockType = 'stone';
                    if (y === height - 1) {
                        blockType = 'grass';
                    } else if (y === height - 2) {
                        blockType = Math.random() > 0.7 ? 'stone' : 'grass';
                    }
                    
                    this.addBlock(x, y, z, blockType);
                }
            }
        }
        
        // 添加一些随机的树
        for (let i = 0; i < 10; i++) {
            const x = Math.floor(Math.random() * 40) - 20;
            const z = Math.floor(Math.random() * 40) - 20;
            this.generateTree(x, height, z);
        }
    }
    
    generateTree(x, y, z) {
        // 树干
        for (let i = 0; i < 4; i++) {
            this.addBlock(x, y + i, z, 'wood');
        }
        
        // 树叶
        for (let dx = -1; dx <= 1; dx++) {
            for (let dz = -1; dz <= 1; dz++) {
                for (let dy = 0; dy < 2; dy++) {
                    if (Math.random() > 0.3) {
                        this.addBlock(x + dx, y + 3 + dy, z + dz, 'grass');
                    }
                }
            }
        }
    }
    
    addBlock(x, y, z, type) {
        const geometry = new THREE.BoxGeometry(1, 1, 1);
        const material = new THREE.MeshLambertMaterial({ color: this.blockTypes[type] });
        const block = new THREE.Mesh(geometry, material);
        
        block.position.set(x, y, z);
        block.castShadow = true;
        block.receiveShadow = true;
        block.userData = { type: type, x: x, y: y, z: z };
        
        this.scene.add(block);
        this.world[`${x},${y},${z}`] = block;
        this.blockCount++;
        this.updateUI();
    }
    
    removeBlock(x, y, z) {
        const key = `${x},${y},${z}`;
        if (this.world[key]) {
            this.scene.remove(this.world[key]);
            delete this.world[key];
            this.blockCount--;
            this.updateUI();
        }
    }
    
    getBlock(x, y, z) {
        return this.world[`${x},${y},${z}`];
    }
    
    setupEventListeners() {
        // 键盘事件
        document.addEventListener('keydown', (event) => {
            switch(event.code) {
                case 'KeyW': this.controls.forward = true; break;
                case 'KeyS': this.controls.backward = true; break;
                case 'KeyA': this.controls.left = true; break;
                case 'KeyD': this.controls.right = true; break;
                case 'Space': 
                    event.preventDefault();
                    this.controls.jump = true; 
                    break;
                case 'Digit1': this.selectBlock('grass'); break;
                case 'Digit2': this.selectBlock('stone'); break;
                case 'Digit3': this.selectBlock('wood'); break;
                case 'Digit4': this.selectBlock('sand'); break;
                case 'Digit5': this.selectBlock('water'); break;
            }
        });
        
        document.addEventListener('keyup', (event) => {
            switch(event.code) {
                case 'KeyW': this.controls.forward = false; break;
                case 'KeyS': this.controls.backward = false; break;
                case 'KeyA': this.controls.left = false; break;
                case 'KeyD': this.controls.right = false; break;
                case 'Space': this.controls.jump = false; break;
            }
        });
        
        // 鼠标事件
        document.addEventListener('mousemove', (event) => {
            if (document.pointerLockElement === document.getElementById('gameCanvas')) {
                this.mouse.x -= event.movementX * this.mouse.sensitivity; // 修复左右方向
                this.mouse.y += event.movementY * this.mouse.sensitivity;
                this.mouse.y = Math.max(-Math.PI/2, Math.min(Math.PI/2, this.mouse.y));
            }
        });
        
        document.addEventListener('click', (event) => {
            if (event.button === 0) { // 左键
                this.breakBlock();
            }
        });
        
        document.addEventListener('contextmenu', (event) => {
            event.preventDefault();
            this.placeBlock();
        });
        
        // 窗口大小调整
        window.addEventListener('resize', () => {
            this.camera.aspect = window.innerWidth / window.innerHeight;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(window.innerWidth, window.innerHeight);
        });
        
        // 物品栏点击
        document.querySelectorAll('.inventory-slot').forEach(slot => {
            slot.addEventListener('click', () => {
                this.selectBlock(slot.dataset.block);
            });
        });
    }
    
    selectBlock(type) {
        this.selectedBlock = type;
        document.querySelectorAll('.inventory-slot').forEach(slot => {
            slot.classList.remove('active');
        });
        document.querySelector(`[data-block="${type}"]`).classList.add('active');
    }
    
    breakBlock() {
        const intersect = this.getTargetBlock();
        if (intersect) {
            const block = intersect.object;
            this.removeBlock(block.userData.x, block.userData.y, block.userData.z);
        }
    }
    
    placeBlock() {
        const intersect = this.getTargetBlock();
        if (intersect) {
            const face = intersect.face;
            const block = intersect.object;
            const normal = face.normal.clone();
            
            const newX = block.userData.x + normal.x;
            const newY = block.userData.y + normal.y;
            const newZ = block.userData.z + normal.z;
            
            // 检查是否与玩家位置冲突
            const playerPos = this.player.position.clone().round();
            if (!(newX === playerPos.x && (newY === playerPos.y || newY === playerPos.y - 1) && newZ === playerPos.z)) {
                this.addBlock(newX, newY, newZ, this.selectedBlock);
            }
        }
    }
    
    getTargetBlock() {
        this.raycaster.setFromCamera(new THREE.Vector2(0, 0), this.camera);
        const intersects = this.raycaster.intersectObjects(Object.values(this.world));
        return intersects.length > 0 ? intersects[0] : null;
    }
    
    updatePlayer() {
        const speed = 0.1;
        const jumpPower = 0.15;
        
        // 应用重力
        this.player.velocity.y -= 0.01;
        
        // 移动控制
        const direction = new THREE.Vector3();
        if (this.controls.forward) direction.z -= 1;
        if (this.controls.backward) direction.z += 1;
        if (this.controls.left) direction.x -= 1;
        if (this.controls.right) direction.x += 1;
        
        direction.normalize();
        direction.multiplyScalar(speed);
        
        // 应用相机旋转到移动方向
        direction.applyAxisAngle(new THREE.Vector3(0, 1, 0), this.mouse.x);
        
        this.player.velocity.x = direction.x;
        this.player.velocity.z = direction.z;
        
        // 跳跃
        if (this.controls.jump && this.player.onGround) {
            this.player.velocity.y = jumpPower;
            this.player.onGround = false;
        }
        
        // 更新位置
        this.player.position.add(this.player.velocity);
        
        // 碰撞检测
        this.checkCollisions();
        
        // 更新相机
        this.camera.position.copy(this.player.position);
        this.camera.rotation.set(this.mouse.y, this.mouse.x, 0);
        
        this.updateUI();
    }
    
    checkCollisions() {
        const pos = this.player.position;
        const blockBelow = this.getBlock(Math.floor(pos.x), Math.floor(pos.y - 1), Math.floor(pos.z));
        
        if (blockBelow) {
            if (pos.y <= Math.floor(pos.y) + 1) {
                pos.y = Math.floor(pos.y) + 1;
                this.player.velocity.y = 0;
                this.player.onGround = true;
            }
        } else {
            this.player.onGround = false;
        }
        
        // 防止掉出世界
        if (pos.y < -10) {
            pos.set(0, 10, 0);
            this.player.velocity.set(0, 0, 0);
        }
    }
    
    updateUI() {
        const pos = this.player.position;
        document.getElementById('position').textContent = 
            `${Math.floor(pos.x)}, ${Math.floor(pos.y)}, ${Math.floor(pos.z)}`;
        document.getElementById('blockCount').textContent = this.blockCount;
    }
    
    gameLoop() {
        requestAnimationFrame(() => this.gameLoop());
        
        this.updatePlayer();
        this.renderer.render(this.scene, this.camera);
    }
}

// 全局变量
let game;

function startGame() {
    document.getElementById('instructions').classList.add('hidden');
    
    // 请求指针锁定
    const canvas = document.getElementById('gameCanvas');
    canvas.requestPointerLock();
    
    // 创建游戏实例
    game = new MinecraftGame();
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('我的世界游戏已准备就绪！');
});
